# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  "integrity" "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw=="
  "resolved" "https://mirrors.tencent.com/npm/@ampproject/remapping/-/remapping-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@ant-design/colors@^7.0.0", "@ant-design/colors@^7.2.1":
  "integrity" "sha512-lCHDcEzieu4GA3n8ELeZ5VQ8pKQAWcGGLRTQ50aQM2iqPpq2evTxER84jfdPvsPAtEcZ7m44NI45edFMo8oOYQ=="
  "resolved" "https://mirrors.tencent.com/npm/@ant-design/colors/-/colors-7.2.1.tgz"
  "version" "7.2.1"
  dependencies:
    "@ant-design/fast-color" "^2.0.6"

"@ant-design/cssinjs-utils@^1.1.3":
  "integrity" "sha512-nOoQMLW1l+xR1Co8NFVYiP8pZp3VjIIzqV6D6ShYF2ljtdwWJn5WSsH+7kvCktXL/yhEtWURKOfH5Xz/gzlwsg=="
  "resolved" "https://mirrors.tencent.com/npm/@ant-design/cssinjs-utils/-/cssinjs-utils-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "@ant-design/cssinjs" "^1.21.0"
    "@babel/runtime" "^7.23.2"
    "rc-util" "^5.38.0"

"@ant-design/cssinjs@^1.21.0", "@ant-design/cssinjs@^1.23.0":
  "integrity" "sha512-7GAg9bD/iC9ikWatU9ym+P9ugJhi/WbsTWzcKN6T4gU0aehsprtke1UAaaSxxkjjmkJb3llet/rbUSLPgwlY4w=="
  "resolved" "https://mirrors.tencent.com/npm/@ant-design/cssinjs/-/cssinjs-1.23.0.tgz"
  "version" "1.23.0"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "@emotion/hash" "^0.8.0"
    "@emotion/unitless" "^0.7.5"
    "classnames" "^2.3.1"
    "csstype" "^3.1.3"
    "rc-util" "^5.35.0"
    "stylis" "^4.3.4"

"@ant-design/fast-color@^2.0.6":
  "integrity" "sha512-y2217gk4NqL35giHl72o6Zzqji9O7vHh9YmhUVkPtAOpoTCH4uWxo/pr4VE8t0+ChEPs0qo4eJRC5Q1eXWo3vA=="
  "resolved" "https://mirrors.tencent.com/npm/@ant-design/fast-color/-/fast-color-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "@babel/runtime" "^7.24.7"

"@ant-design/icons-svg@^4.4.0":
  "integrity" "sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA=="
  "resolved" "https://mirrors.tencent.com/npm/@ant-design/icons-svg/-/icons-svg-4.4.2.tgz"
  "version" "4.4.2"

"@ant-design/icons@^5.6.1":
  "integrity" "sha512-0/xS39c91WjPAZOWsvi1//zjx6kAp4kxWwctR6kuU6p133w8RU0D2dSCvZC19uQyharg/sAvYxGYWl01BbZZfg=="
  "resolved" "https://mirrors.tencent.com/npm/@ant-design/icons/-/icons-5.6.1.tgz"
  "version" "5.6.1"
  dependencies:
    "@ant-design/colors" "^7.0.0"
    "@ant-design/icons-svg" "^4.4.0"
    "@babel/runtime" "^7.24.8"
    "classnames" "^2.2.6"
    "rc-util" "^5.31.1"

"@ant-design/react-slick@~1.1.2":
  "integrity" "sha512-EzlvzE6xQUBrZuuhSAFTdsr4P2bBBHGZwKFemEfq8gIGyIQCxalYfZW/T2ORbtQx5rU69o+WycP3exY/7T1hGA=="
  "resolved" "https://mirrors.tencent.com/npm/@ant-design/react-slick/-/react-slick-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/runtime" "^7.10.4"
    "classnames" "^2.2.5"
    "json2mq" "^0.2.0"
    "resize-observer-polyfill" "^1.5.1"
    "throttle-debounce" "^5.0.0"

"@babel/code-frame@^7.27.1":
  "integrity" "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/code-frame/-/code-frame-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    "js-tokens" "^4.0.0"
    "picocolors" "^1.1.1"

"@babel/compat-data@^7.27.2":
  "integrity" "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/compat-data/-/compat-data-7.27.5.tgz"
  "version" "7.27.5"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.26.10":
  "integrity" "sha512-bXYxrXFubeYdvB0NhD/NBB3Qi6aZeV20GOWVI47t2dkecCEoneR4NPVcb7abpXDEvejgrUfFtG6vG/zxAKmg+g=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/core/-/core-7.27.4.tgz"
  "version" "7.27.4"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.27.3"
    "@babel/helpers" "^7.27.4"
    "@babel/parser" "^7.27.4"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.27.4"
    "@babel/types" "^7.27.3"
    "convert-source-map" "^2.0.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.3"
    "semver" "^6.3.1"

"@babel/generator@^7.27.3":
  "integrity" "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/generator/-/generator-7.27.5.tgz"
  "version" "7.27.5"
  dependencies:
    "@babel/parser" "^7.27.5"
    "@babel/types" "^7.27.3"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    "jsesc" "^3.0.2"

"@babel/helper-compilation-targets@^7.27.2":
  "integrity" "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    "browserslist" "^4.24.0"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.1"

"@babel/helper-module-imports@^7.27.1":
  "integrity" "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.3":
  "integrity" "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz"
  "version" "7.27.3"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-plugin-utils@^7.27.1":
  "integrity" "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-string-parser@^7.27.1":
  "integrity" "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-identifier@^7.27.1":
  "integrity" "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-option@^7.27.1":
  "integrity" "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helpers@^7.27.4":
  "integrity" "sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/helpers/-/helpers-7.27.6.tgz"
  "version" "7.27.6"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.6"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.27.2", "@babel/parser@^7.27.4", "@babel/parser@^7.27.5":
  "integrity" "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/parser/-/parser-7.27.5.tgz"
  "version" "7.27.5"
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/plugin-transform-react-jsx-self@^7.25.9":
  "integrity" "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-source@^7.25.9":
  "integrity" "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/runtime@^7.10.1", "@babel/runtime@^7.10.4", "@babel/runtime@^7.11.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.16.7", "@babel/runtime@^7.18.0", "@babel/runtime@^7.18.3", "@babel/runtime@^7.20.0", "@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0", "@babel/runtime@^7.22.5", "@babel/runtime@^7.23.2", "@babel/runtime@^7.23.6", "@babel/runtime@^7.23.9", "@babel/runtime@^7.24.4", "@babel/runtime@^7.24.7", "@babel/runtime@^7.24.8", "@babel/runtime@^7.25.7", "@babel/runtime@^7.26.0":
  "integrity" "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/runtime/-/runtime-7.27.6.tgz"
  "version" "7.27.6"

"@babel/template@^7.27.2":
  "integrity" "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/template/-/template-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3", "@babel/traverse@^7.27.4":
  "integrity" "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/traverse/-/traverse-7.27.4.tgz"
  "version" "7.27.4"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.3"
    "@babel/parser" "^7.27.4"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"
    "debug" "^4.3.1"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.27.1", "@babel/types@^7.27.3", "@babel/types@^7.27.6":
  "integrity" "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q=="
  "resolved" "https://mirrors.tencent.com/npm/@babel/types/-/types-7.27.6.tgz"
  "version" "7.27.6"
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@emotion/hash@^0.8.0":
  "integrity" "sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow=="
  "resolved" "https://mirrors.tencent.com/npm/@emotion/hash/-/hash-0.8.0.tgz"
  "version" "0.8.0"

"@emotion/unitless@^0.7.5":
  "integrity" "sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg=="
  "resolved" "https://mirrors.tencent.com/npm/@emotion/unitless/-/unitless-0.7.5.tgz"
  "version" "0.7.5"

"@esbuild/aix-ppc64@0.25.5":
  "integrity" "sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/android-arm@0.25.5":
  "integrity" "sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/android-arm/-/android-arm-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/android-arm64@0.25.5":
  "integrity" "sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/android-x64@0.25.5":
  "integrity" "sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/android-x64/-/android-x64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/darwin-arm64@0.25.5":
  "integrity" "sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/darwin-x64@0.25.5":
  "integrity" "sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/freebsd-arm64@0.25.5":
  "integrity" "sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/freebsd-x64@0.25.5":
  "integrity" "sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/linux-arm@0.25.5":
  "integrity" "sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/linux-arm64@0.25.5":
  "integrity" "sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/linux-ia32@0.25.5":
  "integrity" "sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/linux-loong64@0.25.5":
  "integrity" "sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/linux-mips64el@0.25.5":
  "integrity" "sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/linux-ppc64@0.25.5":
  "integrity" "sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/linux-riscv64@0.25.5":
  "integrity" "sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/linux-s390x@0.25.5":
  "integrity" "sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/linux-x64@0.25.5":
  "integrity" "sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/netbsd-arm64@0.25.5":
  "integrity" "sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/netbsd-x64@0.25.5":
  "integrity" "sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/openbsd-arm64@0.25.5":
  "integrity" "sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/openbsd-x64@0.25.5":
  "integrity" "sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/sunos-x64@0.25.5":
  "integrity" "sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/win32-arm64@0.25.5":
  "integrity" "sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/win32-ia32@0.25.5":
  "integrity" "sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz"
  "version" "0.25.5"

"@esbuild/win32-x64@0.25.5":
  "integrity" "sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g=="
  "resolved" "https://mirrors.tencent.com/npm/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz"
  "version" "0.25.5"

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.7.0":
  "integrity" "sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw=="
  "resolved" "https://mirrors.tencent.com/npm/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz"
  "version" "4.7.0"
  dependencies:
    "eslint-visitor-keys" "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.12.1":
  "integrity" "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ=="
  "resolved" "https://mirrors.tencent.com/npm/@eslint-community/regexpp/-/regexpp-4.12.1.tgz"
  "version" "4.12.1"

"@eslint/config-array@^0.20.0":
  "integrity" "sha512-fxlS1kkIjx8+vy2SjuCB94q3htSNrufYTXubwiBFeaQHbH6Ipi43gFJq2zCMt6PHhImH3Xmr0NksKDvchWlpQQ=="
  "resolved" "https://mirrors.tencent.com/npm/@eslint/config-array/-/config-array-0.20.0.tgz"
  "version" "0.20.0"
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    "debug" "^4.3.1"
    "minimatch" "^3.1.2"

"@eslint/config-helpers@^0.2.1":
  "integrity" "sha512-+GPzk8PlG0sPpzdU5ZvIRMPidzAnZDl/s9L+y13iodqvb8leL53bTannOrQ/Im7UkpsmFU5Ily5U60LWixnmLg=="
  "resolved" "https://mirrors.tencent.com/npm/@eslint/config-helpers/-/config-helpers-0.2.2.tgz"
  "version" "0.2.2"

"@eslint/core@^0.14.0":
  "integrity" "sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg=="
  "resolved" "https://mirrors.tencent.com/npm/@eslint/core/-/core-0.14.0.tgz"
  "version" "0.14.0"
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3.3.1":
  "integrity" "sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ=="
  "resolved" "https://mirrors.tencent.com/npm/@eslint/eslintrc/-/eslintrc-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.3.2"
    "espree" "^10.0.1"
    "globals" "^14.0.0"
    "ignore" "^5.2.0"
    "import-fresh" "^3.2.1"
    "js-yaml" "^4.1.0"
    "minimatch" "^3.1.2"
    "strip-json-comments" "^3.1.1"

"@eslint/js@^9.25.0", "@eslint/js@9.28.0":
  "integrity" "sha512-fnqSjGWd/CoIp4EXIxWVK/sHA6DOHN4+8Ix2cX5ycOY7LG0UY8nHCU5pIp2eaE1Mc7Qd8kHspYNzYXT2ojPLzg=="
  "resolved" "https://mirrors.tencent.com/npm/@eslint/js/-/js-9.28.0.tgz"
  "version" "9.28.0"

"@eslint/object-schema@^2.1.6":
  "integrity" "sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA=="
  "resolved" "https://mirrors.tencent.com/npm/@eslint/object-schema/-/object-schema-2.1.6.tgz"
  "version" "2.1.6"

"@eslint/plugin-kit@^0.3.1":
  "integrity" "sha512-0J+zgWxHN+xXONWIyPWKFMgVuJoZuGiIFu8yxk7RJjxkzpGmyja5wRFqZIVtjDVOQpV+Rw0iOAjYPE2eQyjr0w=="
  "resolved" "https://mirrors.tencent.com/npm/@eslint/plugin-kit/-/plugin-kit-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "@eslint/core" "^0.14.0"
    "levn" "^0.4.1"

"@humanfs/core@^0.19.1":
  "integrity" "sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA=="
  "resolved" "https://mirrors.tencent.com/npm/@humanfs/core/-/core-0.19.1.tgz"
  "version" "0.19.1"

"@humanfs/node@^0.16.6":
  "integrity" "sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw=="
  "resolved" "https://mirrors.tencent.com/npm/@humanfs/node/-/node-0.16.6.tgz"
  "version" "0.16.6"
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.3.0"

"@humanwhocodes/module-importer@^1.0.1":
  "integrity" "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA=="
  "resolved" "https://mirrors.tencent.com/npm/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  "version" "1.0.1"

"@humanwhocodes/retry@^0.3.0":
  "integrity" "sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA=="
  "resolved" "https://mirrors.tencent.com/npm/@humanwhocodes/retry/-/retry-0.3.1.tgz"
  "version" "0.3.1"

"@humanwhocodes/retry@^0.4.2":
  "integrity" "sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ=="
  "resolved" "https://mirrors.tencent.com/npm/@humanwhocodes/retry/-/retry-0.4.3.tgz"
  "version" "0.4.3"

"@jridgewell/gen-mapping@^0.3.5":
  "integrity" "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA=="
  "resolved" "https://mirrors.tencent.com/npm/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz"
  "version" "0.3.8"
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  "integrity" "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw=="
  "resolved" "https://mirrors.tencent.com/npm/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  "version" "3.1.2"

"@jridgewell/set-array@^1.2.1":
  "integrity" "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A=="
  "resolved" "https://mirrors.tencent.com/npm/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  "version" "1.2.1"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  "integrity" "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ=="
  "resolved" "https://mirrors.tencent.com/npm/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  "version" "1.5.0"

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  "integrity" "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ=="
  "resolved" "https://mirrors.tencent.com/npm/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  "version" "0.3.25"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://mirrors.tencent.com/npm/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://mirrors.tencent.com/npm/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://mirrors.tencent.com/npm/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@rc-component/async-validator@^5.0.3":
  "integrity" "sha512-qgGdcVIF604M9EqjNF0hbUTz42bz/RDtxWdWuU5EQe3hi7M8ob54B6B35rOsvX5eSvIHIzT9iH1R3n+hk3CGfg=="
  "resolved" "https://mirrors.tencent.com/npm/@rc-component/async-validator/-/async-validator-5.0.4.tgz"
  "version" "5.0.4"
  dependencies:
    "@babel/runtime" "^7.24.4"

"@rc-component/color-picker@~2.0.1":
  "integrity" "sha512-WcZYwAThV/b2GISQ8F+7650r5ZZJ043E57aVBFkQ+kSY4C6wdofXgB0hBx+GPGpIU0Z81eETNoDUJMr7oy/P8Q=="
  "resolved" "https://mirrors.tencent.com/npm/@rc-component/color-picker/-/color-picker-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@ant-design/fast-color" "^2.0.6"
    "@babel/runtime" "^7.23.6"
    "classnames" "^2.2.6"
    "rc-util" "^5.38.1"

"@rc-component/context@^1.4.0":
  "integrity" "sha512-kFcNxg9oLRMoL3qki0OMxK+7g5mypjgaaJp/pkOis/6rVxma9nJBF/8kCIuTYHUQNr0ii7MxqE33wirPZLJQ2w=="
  "resolved" "https://mirrors.tencent.com/npm/@rc-component/context/-/context-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "rc-util" "^5.27.0"

"@rc-component/mini-decimal@^1.0.1":
  "integrity" "sha512-jS4E7T9Li2GuYwI6PyiVXmxTiM6b07rlD9Ge8uGZSCz3WlzcG5ZK7g5bbuKNeZ9pgUuPK/5guV781ujdVpm4HQ=="
  "resolved" "https://mirrors.tencent.com/npm/@rc-component/mini-decimal/-/mini-decimal-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@babel/runtime" "^7.18.0"

"@rc-component/mutate-observer@^1.1.0":
  "integrity" "sha512-QjrOsDXQusNwGZPf4/qRQasg7UFEj06XiCJ8iuiq/Io7CrHrgVi6Uuetw60WAMG1799v+aM8kyc+1L/GBbHSlw=="
  "resolved" "https://mirrors.tencent.com/npm/@rc-component/mutate-observer/-/mutate-observer-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.3.2"
    "rc-util" "^5.24.4"

"@rc-component/portal@^1.0.0-8", "@rc-component/portal@^1.0.0-9", "@rc-component/portal@^1.0.2", "@rc-component/portal@^1.1.0", "@rc-component/portal@^1.1.1":
  "integrity" "sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg=="
  "resolved" "https://mirrors.tencent.com/npm/@rc-component/portal/-/portal-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.3.2"
    "rc-util" "^5.24.4"

"@rc-component/qrcode@~1.0.0":
  "integrity" "sha512-L+rZ4HXP2sJ1gHMGHjsg9jlYBX/SLN2D6OxP9Zn3qgtpMWtO2vUfxVFwiogHpAIqs54FnALxraUy/BCO1yRIgg=="
  "resolved" "https://mirrors.tencent.com/npm/@rc-component/qrcode/-/qrcode-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "@babel/runtime" "^7.24.7"
    "classnames" "^2.3.2"
    "rc-util" "^5.38.0"

"@rc-component/tour@~1.15.1":
  "integrity" "sha512-Tr2t7J1DKZUpfJuDZWHxyxWpfmj8EZrqSgyMZ+BCdvKZ6r1UDsfU46M/iWAAFBy961Ssfom2kv5f3UcjIL2CmQ=="
  "resolved" "https://mirrors.tencent.com/npm/@rc-component/tour/-/tour-1.15.1.tgz"
  "version" "1.15.1"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/portal" "^1.0.0-9"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.3.2"
    "rc-util" "^5.24.4"

"@rc-component/trigger@^2.0.0", "@rc-component/trigger@^2.1.1", "@rc-component/trigger@^2.2.6":
  "integrity" "sha512-/9zuTnWwhQ3S3WT1T8BubuFTT46kvnXgaERR9f4BTKyn61/wpf/BvbImzYBubzJibU707FxwbKszLlHjcLiv1Q=="
  "resolved" "https://mirrors.tencent.com/npm/@rc-component/trigger/-/trigger-2.2.6.tgz"
  "version" "2.2.6"
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@rc-component/portal" "^1.1.0"
    "classnames" "^2.3.2"
    "rc-motion" "^2.0.0"
    "rc-resize-observer" "^1.3.1"
    "rc-util" "^5.44.0"

"@rolldown/pluginutils@1.0.0-beta.9":
  "integrity" "sha512-e9MeMtVWo186sgvFFJOPGy7/d2j2mZhLJIdVW0C/xDluuOvymEATqz6zKsP0ZmXGzQtqlyjz5sC1sYQUoJG98w=="
  "resolved" "https://mirrors.tencent.com/npm/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9.tgz"
  "version" "1.0.0-beta.9"

"@rollup/rollup-android-arm-eabi@4.41.1":
  "integrity" "sha512-NELNvyEWZ6R9QMkiytB4/L4zSEaBC03KIXEghptLGLZWJ6VPrL63ooZQCOnlx36aQPGhzuOMwDerC1Eb2VmrLw=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-android-arm64@4.41.1":
  "integrity" "sha512-DXdQe1BJ6TK47ukAoZLehRHhfKnKg9BjnQYUu9gzhI8Mwa1d2fzxA1aw2JixHVl403bwp1+/o/NhhHtxWJBgEA=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-darwin-arm64@4.41.1":
  "integrity" "sha512-5afxvwszzdulsU2w8JKWwY8/sJOLPzf0e1bFuvcW5h9zsEg+RQAojdW0ux2zyYAz7R8HvvzKCjLNJhVq965U7w=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-darwin-x64@4.41.1":
  "integrity" "sha512-egpJACny8QOdHNNMZKf8xY0Is6gIMz+tuqXlusxquWu3F833DcMwmGM7WlvCO9sB3OsPjdC4U0wHw5FabzCGZg=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-freebsd-arm64@4.41.1":
  "integrity" "sha512-DBVMZH5vbjgRk3r0OzgjS38z+atlupJ7xfKIDJdZZL6sM6wjfDNo64aowcLPKIx7LMQi8vybB56uh1Ftck/Atg=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-freebsd-x64@4.41.1":
  "integrity" "sha512-3FkydeohozEskBxNWEIbPfOE0aqQgB6ttTkJ159uWOFn42VLyfAiyD9UK5mhu+ItWzft60DycIN1Xdgiy8o/SA=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-arm-gnueabihf@4.41.1":
  "integrity" "sha512-wC53ZNDgt0pqx5xCAgNunkTzFE8GTgdZ9EwYGVcg+jEjJdZGtq9xPjDnFgfFozQI/Xm1mh+D9YlYtl+ueswNEg=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-arm-musleabihf@4.41.1":
  "integrity" "sha512-jwKCca1gbZkZLhLRtsrka5N8sFAaxrGz/7wRJ8Wwvq3jug7toO21vWlViihG85ei7uJTpzbXZRcORotE+xyrLA=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-arm64-gnu@4.41.1":
  "integrity" "sha512-g0UBcNknsmmNQ8V2d/zD2P7WWfJKU0F1nu0k5pW4rvdb+BIqMm8ToluW/eeRmxCared5dD76lS04uL4UaNgpNA=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-arm64-musl@4.41.1":
  "integrity" "sha512-XZpeGB5TKEZWzIrj7sXr+BEaSgo/ma/kCgrZgL0oo5qdB1JlTzIYQKel/RmhT6vMAvOdM2teYlAaOGJpJ9lahg=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-loongarch64-gnu@4.41.1":
  "integrity" "sha512-bkCfDJ4qzWfFRCNt5RVV4DOw6KEgFTUZi2r2RuYhGWC8WhCA8lCAJhDeAmrM/fdiAH54m0mA0Vk2FGRPyzI+tw=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-powerpc64le-gnu@4.41.1":
  "integrity" "sha512-3mr3Xm+gvMX+/8EKogIZSIEF0WUu0HL9di+YWlJpO8CQBnoLAEL/roTCxuLncEdgcfJcvA4UMOf+2dnjl4Ut1A=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-riscv64-gnu@4.41.1":
  "integrity" "sha512-3rwCIh6MQ1LGrvKJitQjZFuQnT2wxfU+ivhNBzmxXTXPllewOF7JR1s2vMX/tWtUYFgphygxjqMl76q4aMotGw=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-riscv64-musl@4.41.1":
  "integrity" "sha512-LdIUOb3gvfmpkgFZuccNa2uYiqtgZAz3PTzjuM5bH3nvuy9ty6RGc/Q0+HDFrHrizJGVpjnTZ1yS5TNNjFlklw=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-s390x-gnu@4.41.1":
  "integrity" "sha512-oIE6M8WC9ma6xYqjvPhzZYk6NbobIURvP/lEbh7FWplcMO6gn7MM2yHKA1eC/GvYwzNKK/1LYgqzdkZ8YFxR8g=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-x64-gnu@4.41.1":
  "integrity" "sha512-cWBOvayNvA+SyeQMp79BHPK8ws6sHSsYnK5zDcsC3Hsxr1dgTABKjMnMslPq1DvZIp6uO7kIWhiGwaTdR4Og9A=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-linux-x64-musl@4.41.1":
  "integrity" "sha512-y5CbN44M+pUCdGDlZFzGGBSKCA4A/J2ZH4edTYSSxFg7ce1Xt3GtydbVKWLlzL+INfFIZAEg1ZV6hh9+QQf9YQ=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-win32-arm64-msvc@4.41.1":
  "integrity" "sha512-lZkCxIrjlJlMt1dLO/FbpZbzt6J/A8p4DnqzSa4PWqPEUUUnzXLeki/iyPLfV0BmHItlYgHUqJe+3KiyydmiNQ=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-win32-ia32-msvc@4.41.1":
  "integrity" "sha512-+psFT9+pIh2iuGsxFYYa/LhS5MFKmuivRsx9iPJWNSGbh2XVEjk90fmpUEjCnILPEPJnikAU6SFDiEUyOv90Pg=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.41.1.tgz"
  "version" "4.41.1"

"@rollup/rollup-win32-x64-msvc@4.41.1":
  "integrity" "sha512-Wq2zpapRYLfi4aKxf2Xff0tN+7slj2d4R87WEzqw7ZLsVvO5zwYCIuEGSZYiK41+GlwUo1HiR+GdkLEJnCKTCw=="
  "resolved" "https://mirrors.tencent.com/npm/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.41.1.tgz"
  "version" "4.41.1"

"@types/babel__core@^7.20.5":
  "integrity" "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA=="
  "resolved" "https://mirrors.tencent.com/npm/@types/babel__core/-/babel__core-7.20.5.tgz"
  "version" "7.20.5"
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg=="
  "resolved" "https://mirrors.tencent.com/npm/@types/babel__generator/-/babel__generator-7.27.0.tgz"
  "version" "7.27.0"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  "integrity" "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A=="
  "resolved" "https://mirrors.tencent.com/npm/@types/babel__template/-/babel__template-7.4.4.tgz"
  "version" "7.4.4"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  "integrity" "sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng=="
  "resolved" "https://mirrors.tencent.com/npm/@types/babel__traverse/-/babel__traverse-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/types" "^7.20.7"

"@types/estree@^1.0.6":
  "integrity" "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w=="
  "resolved" "https://mirrors.tencent.com/npm/@types/estree/-/estree-1.0.8.tgz"
  "version" "1.0.8"

"@types/estree@1.0.7":
  "integrity" "sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ=="
  "resolved" "https://mirrors.tencent.com/npm/@types/estree/-/estree-1.0.7.tgz"
  "version" "1.0.7"

"@types/json-schema@^7.0.15":
  "integrity" "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA=="
  "resolved" "https://mirrors.tencent.com/npm/@types/json-schema/-/json-schema-7.0.15.tgz"
  "version" "7.0.15"

"@types/react-dom@^19.1.2":
  "integrity" "sha512-4hOiT/dwO8Ko0gV1m/TJZYk3y0KBnY9vzDh7W+DH17b2HFSOGgdj33dhihPeuy3l0q23+4e+hoXHV6hCC4dCXw=="
  "resolved" "https://mirrors.tencent.com/npm/@types/react-dom/-/react-dom-19.1.6.tgz"
  "version" "19.1.6"

"@types/react@^19.0.0", "@types/react@^19.1.2":
  "integrity" "sha512-JeG0rEWak0N6Itr6QUx+X60uQmN+5t3j9r/OVDtWzFXKaj6kD1BwJzOksD0FF6iWxZlbE1kB0q9vtnU2ekqa1Q=="
  "resolved" "https://mirrors.tencent.com/npm/@types/react/-/react-19.1.6.tgz"
  "version" "19.1.6"
  dependencies:
    "csstype" "^3.0.2"

"@typescript-eslint/eslint-plugin@8.33.1":
  "integrity" "sha512-TDCXj+YxLgtvxvFlAvpoRv9MAncDLBV2oT9Bd7YBGC/b/sEURoOYuIwLI99rjWOfY3QtDzO+mk0n4AmdFExW8A=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.33.1.tgz"
  "version" "8.33.1"
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.33.1"
    "@typescript-eslint/type-utils" "8.33.1"
    "@typescript-eslint/utils" "8.33.1"
    "@typescript-eslint/visitor-keys" "8.33.1"
    "graphemer" "^1.4.0"
    "ignore" "^7.0.0"
    "natural-compare" "^1.4.0"
    "ts-api-utils" "^2.1.0"

"@typescript-eslint/parser@^8.33.1", "@typescript-eslint/parser@8.33.1":
  "integrity" "sha512-qwxv6dq682yVvgKKp2qWwLgRbscDAYktPptK4JPojCwwi3R9cwrvIxS4lvBpzmcqzR4bdn54Z0IG1uHFskW4dA=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/parser/-/parser-8.33.1.tgz"
  "version" "8.33.1"
  dependencies:
    "@typescript-eslint/scope-manager" "8.33.1"
    "@typescript-eslint/types" "8.33.1"
    "@typescript-eslint/typescript-estree" "8.33.1"
    "@typescript-eslint/visitor-keys" "8.33.1"
    "debug" "^4.3.4"

"@typescript-eslint/project-service@8.33.1":
  "integrity" "sha512-DZR0efeNklDIHHGRpMpR5gJITQpu6tLr9lDJnKdONTC7vvzOlLAG/wcfxcdxEWrbiZApcoBCzXqU/Z458Za5Iw=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/project-service/-/project-service-8.33.1.tgz"
  "version" "8.33.1"
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.33.1"
    "@typescript-eslint/types" "^8.33.1"
    "debug" "^4.3.4"

"@typescript-eslint/scope-manager@8.33.1":
  "integrity" "sha512-dM4UBtgmzHR9bS0Rv09JST0RcHYearoEoo3pG5B6GoTR9XcyeqX87FEhPo+5kTvVfKCvfHaHrcgeJQc6mrDKrA=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/scope-manager/-/scope-manager-8.33.1.tgz"
  "version" "8.33.1"
  dependencies:
    "@typescript-eslint/types" "8.33.1"
    "@typescript-eslint/visitor-keys" "8.33.1"

"@typescript-eslint/tsconfig-utils@^8.33.1", "@typescript-eslint/tsconfig-utils@8.33.1":
  "integrity" "sha512-STAQsGYbHCF0/e+ShUQ4EatXQ7ceh3fBCXkNU7/MZVKulrlq1usH7t2FhxvCpuCi5O5oi1vmVaAjrGeL71OK1g=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.33.1.tgz"
  "version" "8.33.1"

"@typescript-eslint/type-utils@8.33.1":
  "integrity" "sha512-1cG37d9xOkhlykom55WVwG2QRNC7YXlxMaMzqw2uPeJixBFfKWZgaP/hjAObqMN/u3fr5BrTwTnc31/L9jQ2ww=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/type-utils/-/type-utils-8.33.1.tgz"
  "version" "8.33.1"
  dependencies:
    "@typescript-eslint/typescript-estree" "8.33.1"
    "@typescript-eslint/utils" "8.33.1"
    "debug" "^4.3.4"
    "ts-api-utils" "^2.1.0"

"@typescript-eslint/types@^8.33.1", "@typescript-eslint/types@8.33.1":
  "integrity" "sha512-xid1WfizGhy/TKMTwhtVOgalHwPtV8T32MS9MaH50Cwvz6x6YqRIPdD2WvW0XaqOzTV9p5xdLY0h/ZusU5Lokg=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/types/-/types-8.33.1.tgz"
  "version" "8.33.1"

"@typescript-eslint/typescript-estree@8.33.1":
  "integrity" "sha512-+s9LYcT8LWjdYWu7IWs7FvUxpQ/DGkdjZeE/GGulHvv8rvYwQvVaUZ6DE+j5x/prADUgSbbCWZ2nPI3usuVeOA=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/typescript-estree/-/typescript-estree-8.33.1.tgz"
  "version" "8.33.1"
  dependencies:
    "@typescript-eslint/project-service" "8.33.1"
    "@typescript-eslint/tsconfig-utils" "8.33.1"
    "@typescript-eslint/types" "8.33.1"
    "@typescript-eslint/visitor-keys" "8.33.1"
    "debug" "^4.3.4"
    "fast-glob" "^3.3.2"
    "is-glob" "^4.0.3"
    "minimatch" "^9.0.4"
    "semver" "^7.6.0"
    "ts-api-utils" "^2.1.0"

"@typescript-eslint/utils@8.33.1":
  "integrity" "sha512-52HaBiEQUaRYqAXpfzWSR2U3gxk92Kw006+xZpElaPMg3C4PgM+A5LqwoQI1f9E5aZ/qlxAZxzm42WX+vn92SQ=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/utils/-/utils-8.33.1.tgz"
  "version" "8.33.1"
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.33.1"
    "@typescript-eslint/types" "8.33.1"
    "@typescript-eslint/typescript-estree" "8.33.1"

"@typescript-eslint/visitor-keys@8.33.1":
  "integrity" "sha512-3i8NrFcZeeDHJ+7ZUuDkGT+UHq+XoFGsymNK2jZCOHcfEzRQ0BdpRtdpSx/Iyf3MHLWIcLS0COuOPibKQboIiQ=="
  "resolved" "https://mirrors.tencent.com/npm/@typescript-eslint/visitor-keys/-/visitor-keys-8.33.1.tgz"
  "version" "8.33.1"
  dependencies:
    "@typescript-eslint/types" "8.33.1"
    "eslint-visitor-keys" "^4.2.0"

"@vitejs/plugin-react@^4.4.1":
  "integrity" "sha512-uPZBqSI0YD4lpkIru6M35sIfylLGTyhGHvDZbNLuMA73lMlwJKz5xweH7FajfcCAc2HnINciejA9qTz0dr0M7A=="
  "resolved" "https://mirrors.tencent.com/npm/@vitejs/plugin-react/-/plugin-react-4.5.1.tgz"
  "version" "4.5.1"
  dependencies:
    "@babel/core" "^7.26.10"
    "@babel/plugin-transform-react-jsx-self" "^7.25.9"
    "@babel/plugin-transform-react-jsx-source" "^7.25.9"
    "@rolldown/pluginutils" "1.0.0-beta.9"
    "@types/babel__core" "^7.20.5"
    "react-refresh" "^0.17.0"

"acorn-jsx@^5.3.2":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://mirrors.tencent.com/npm/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8.14.0":
  "integrity" "sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg=="
  "resolved" "https://mirrors.tencent.com/npm/acorn/-/acorn-8.14.1.tgz"
  "version" "8.14.1"

"ajv@^6.12.4":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://mirrors.tencent.com/npm/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://mirrors.tencent.com/npm/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"antd@^5.25.4":
  "integrity" "sha512-yXdWqq1NJSZnD1HoPZWnWuQJGVYYnB3h0Ufsz4sbt3T0N9SdJ4G9GPpLMk8Gn9zWtwBekfR4THPVZ9uzAyhBHQ=="
  "resolved" "https://mirrors.tencent.com/npm/antd/-/antd-5.25.4.tgz"
  "version" "5.25.4"
  dependencies:
    "@ant-design/colors" "^7.2.1"
    "@ant-design/cssinjs" "^1.23.0"
    "@ant-design/cssinjs-utils" "^1.1.3"
    "@ant-design/fast-color" "^2.0.6"
    "@ant-design/icons" "^5.6.1"
    "@ant-design/react-slick" "~1.1.2"
    "@babel/runtime" "^7.26.0"
    "@rc-component/color-picker" "~2.0.1"
    "@rc-component/mutate-observer" "^1.1.0"
    "@rc-component/qrcode" "~1.0.0"
    "@rc-component/tour" "~1.15.1"
    "@rc-component/trigger" "^2.2.6"
    "classnames" "^2.5.1"
    "copy-to-clipboard" "^3.3.3"
    "dayjs" "^1.11.11"
    "rc-cascader" "~3.34.0"
    "rc-checkbox" "~3.5.0"
    "rc-collapse" "~3.9.0"
    "rc-dialog" "~9.6.0"
    "rc-drawer" "~7.3.0"
    "rc-dropdown" "~4.2.1"
    "rc-field-form" "~2.7.0"
    "rc-image" "~7.12.0"
    "rc-input" "~1.8.0"
    "rc-input-number" "~9.5.0"
    "rc-mentions" "~2.20.0"
    "rc-menu" "~9.16.1"
    "rc-motion" "^2.9.5"
    "rc-notification" "~5.6.4"
    "rc-pagination" "~5.1.0"
    "rc-picker" "~4.11.3"
    "rc-progress" "~4.0.0"
    "rc-rate" "~2.13.1"
    "rc-resize-observer" "^1.4.3"
    "rc-segmented" "~2.7.0"
    "rc-select" "~14.16.8"
    "rc-slider" "~11.1.8"
    "rc-steps" "~6.0.1"
    "rc-switch" "~4.1.0"
    "rc-table" "~7.50.5"
    "rc-tabs" "~15.6.1"
    "rc-textarea" "~1.10.0"
    "rc-tooltip" "~6.4.0"
    "rc-tree" "~5.13.1"
    "rc-tree-select" "~5.27.0"
    "rc-upload" "~4.9.2"
    "rc-util" "^5.44.4"
    "scroll-into-view-if-needed" "^3.1.0"
    "throttle-debounce" "^5.0.2"

"argparse@^2.0.1":
  "integrity" "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
  "resolved" "https://mirrors.tencent.com/npm/argparse/-/argparse-2.0.1.tgz"
  "version" "2.0.1"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://mirrors.tencent.com/npm/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://mirrors.tencent.com/npm/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"brace-expansion@^2.0.1":
  "integrity" "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA=="
  "resolved" "https://mirrors.tencent.com/npm/brace-expansion/-/brace-expansion-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "balanced-match" "^1.0.0"

"braces@^3.0.3":
  "integrity" "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA=="
  "resolved" "https://mirrors.tencent.com/npm/braces/-/braces-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "fill-range" "^7.1.1"

"browserslist@^4.24.0", "browserslist@>= 4.21.0":
  "integrity" "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA=="
  "resolved" "https://mirrors.tencent.com/npm/browserslist/-/browserslist-4.25.0.tgz"
  "version" "4.25.0"
  dependencies:
    "caniuse-lite" "^1.0.30001718"
    "electron-to-chromium" "^1.5.160"
    "node-releases" "^2.0.19"
    "update-browserslist-db" "^1.1.3"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://mirrors.tencent.com/npm/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"caniuse-lite@^1.0.30001718":
  "integrity" "sha512-cOuvmUVtKrtEaoKiO0rSc29jcjwMwX5tOHDy4MgVFEWiUXj4uBMJkwI8MDySkgXidpMiHUcviogAvFi4pA2hDQ=="
  "resolved" "https://mirrors.tencent.com/npm/caniuse-lite/-/caniuse-lite-1.0.30001721.tgz"
  "version" "1.0.30001721"

"chalk@^4.0.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://mirrors.tencent.com/npm/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"classnames@^2.2.1", "classnames@^2.2.3", "classnames@^2.2.5", "classnames@^2.2.6", "classnames@^2.3.1", "classnames@^2.3.2", "classnames@^2.5.1", "classnames@2.x":
  "integrity" "sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow=="
  "resolved" "https://mirrors.tencent.com/npm/classnames/-/classnames-2.5.1.tgz"
  "version" "2.5.1"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://mirrors.tencent.com/npm/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://mirrors.tencent.com/npm/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"compute-scroll-into-view@^3.0.2":
  "integrity" "sha512-VRhuHOLoKYOy4UbilLbUzbYg93XLjv2PncJC50EuTWPA3gaja1UjBsUP/D/9/juV3vQFr6XBEzn9KCAHdUvOHw=="
  "resolved" "https://mirrors.tencent.com/npm/compute-scroll-into-view/-/compute-scroll-into-view-3.1.1.tgz"
  "version" "3.1.1"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://mirrors.tencent.com/npm/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"convert-source-map@^2.0.0":
  "integrity" "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg=="
  "resolved" "https://mirrors.tencent.com/npm/convert-source-map/-/convert-source-map-2.0.0.tgz"
  "version" "2.0.0"

"copy-to-clipboard@^3.3.3":
  "integrity" "sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA=="
  "resolved" "https://mirrors.tencent.com/npm/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz"
  "version" "3.3.3"
  dependencies:
    "toggle-selection" "^1.0.6"

"cross-spawn@^7.0.6":
  "integrity" "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA=="
  "resolved" "https://mirrors.tencent.com/npm/cross-spawn/-/cross-spawn-7.0.6.tgz"
  "version" "7.0.6"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"csstype@^3.0.2", "csstype@^3.1.3":
  "integrity" "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="
  "resolved" "https://mirrors.tencent.com/npm/csstype/-/csstype-3.1.3.tgz"
  "version" "3.1.3"

"dayjs@^1.11.11", "dayjs@>= 1.x":
  "integrity" "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg=="
  "resolved" "https://mirrors.tencent.com/npm/dayjs/-/dayjs-1.11.13.tgz"
  "version" "1.11.13"

"debug@^4.1.0", "debug@^4.3.1", "debug@^4.3.2", "debug@^4.3.4":
  "integrity" "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ=="
  "resolved" "https://mirrors.tencent.com/npm/debug/-/debug-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"deep-is@^0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://mirrors.tencent.com/npm/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"electron-to-chromium@^1.5.160":
  "integrity" "sha512-naiMx1Z6Nb2TxPU6fiFrUrDTjyPMLdTtaOd2oLmG8zVSg2hCWGkhPyxwk+qRmZ1ytwVqUv0u7ZcDA5+ALhaUtw=="
  "resolved" "https://mirrors.tencent.com/npm/electron-to-chromium/-/electron-to-chromium-1.5.165.tgz"
  "version" "1.5.165"

"esbuild@^0.25.0":
  "integrity" "sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ=="
  "resolved" "https://mirrors.tencent.com/npm/esbuild/-/esbuild-0.25.5.tgz"
  "version" "0.25.5"
  dependencies:
    "@esbuild/aix-ppc64" "0.25.5"
    "@esbuild/android-arm" "0.25.5"
    "@esbuild/android-arm64" "0.25.5"
    "@esbuild/android-x64" "0.25.5"
    "@esbuild/darwin-arm64" "0.25.5"
    "@esbuild/darwin-x64" "0.25.5"
    "@esbuild/freebsd-arm64" "0.25.5"
    "@esbuild/freebsd-x64" "0.25.5"
    "@esbuild/linux-arm" "0.25.5"
    "@esbuild/linux-arm64" "0.25.5"
    "@esbuild/linux-ia32" "0.25.5"
    "@esbuild/linux-loong64" "0.25.5"
    "@esbuild/linux-mips64el" "0.25.5"
    "@esbuild/linux-ppc64" "0.25.5"
    "@esbuild/linux-riscv64" "0.25.5"
    "@esbuild/linux-s390x" "0.25.5"
    "@esbuild/linux-x64" "0.25.5"
    "@esbuild/netbsd-arm64" "0.25.5"
    "@esbuild/netbsd-x64" "0.25.5"
    "@esbuild/openbsd-arm64" "0.25.5"
    "@esbuild/openbsd-x64" "0.25.5"
    "@esbuild/sunos-x64" "0.25.5"
    "@esbuild/win32-arm64" "0.25.5"
    "@esbuild/win32-ia32" "0.25.5"
    "@esbuild/win32-x64" "0.25.5"
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.5"
    "@esbuild/android-arm" "0.25.5"
    "@esbuild/android-arm64" "0.25.5"
    "@esbuild/android-x64" "0.25.5"
    "@esbuild/darwin-arm64" "0.25.5"
    "@esbuild/darwin-x64" "0.25.5"
    "@esbuild/freebsd-arm64" "0.25.5"
    "@esbuild/freebsd-x64" "0.25.5"
    "@esbuild/linux-arm" "0.25.5"
    "@esbuild/linux-arm64" "0.25.5"
    "@esbuild/linux-ia32" "0.25.5"
    "@esbuild/linux-loong64" "0.25.5"
    "@esbuild/linux-mips64el" "0.25.5"
    "@esbuild/linux-ppc64" "0.25.5"
    "@esbuild/linux-riscv64" "0.25.5"
    "@esbuild/linux-s390x" "0.25.5"
    "@esbuild/linux-x64" "0.25.5"
    "@esbuild/netbsd-arm64" "0.25.5"
    "@esbuild/netbsd-x64" "0.25.5"
    "@esbuild/openbsd-arm64" "0.25.5"
    "@esbuild/openbsd-x64" "0.25.5"
    "@esbuild/sunos-x64" "0.25.5"
    "@esbuild/win32-arm64" "0.25.5"
    "@esbuild/win32-ia32" "0.25.5"
    "@esbuild/win32-x64" "0.25.5"

"escalade@^3.2.0":
  "integrity" "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="
  "resolved" "https://mirrors.tencent.com/npm/escalade/-/escalade-3.2.0.tgz"
  "version" "3.2.0"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://mirrors.tencent.com/npm/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"eslint-plugin-react-hooks@^5.2.0":
  "integrity" "sha512-+f15FfK64YQwZdJNELETdn5ibXEUQmW1DZL6KXhNnc2heoy/sg9VJJeT7n8TlMWouzWqSWavFkIhHyIbIAEapg=="
  "resolved" "https://mirrors.tencent.com/npm/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz"
  "version" "5.2.0"

"eslint-plugin-react-refresh@^0.4.19":
  "integrity" "sha512-XpbHQ2q5gUF8BGOX4dHe+71qoirYMhApEPZ7sfhF/dNnOF1UXnCMGZf79SFTBO7Bz5YEIT4TMieSlJBWhP9WBA=="
  "resolved" "https://mirrors.tencent.com/npm/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz"
  "version" "0.4.20"

"eslint-scope@^8.3.0":
  "integrity" "sha512-pUNxi75F8MJ/GdeKtVLSbYg4ZI34J6C0C7sbL4YOp2exGwen7ZsuBqKzUhXd0qMQ362yET3z+uPwKeg/0C2XCQ=="
  "resolved" "https://mirrors.tencent.com/npm/eslint-scope/-/eslint-scope-8.3.0.tgz"
  "version" "8.3.0"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-visitor-keys@^3.4.3":
  "integrity" "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag=="
  "resolved" "https://mirrors.tencent.com/npm/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  "version" "3.4.3"

"eslint-visitor-keys@^4.2.0":
  "integrity" "sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw=="
  "resolved" "https://mirrors.tencent.com/npm/eslint-visitor-keys/-/eslint-visitor-keys-4.2.0.tgz"
  "version" "4.2.0"

"eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^8.57.0 || ^9.0.0", "eslint@^9.25.0", "eslint@>=8.40":
  "integrity" "sha512-ocgh41VhRlf9+fVpe7QKzwLj9c92fDiqOj8Y3Sd4/ZmVA4Btx4PlUYPq4pp9JDyupkf1upbEXecxL2mwNV7jPQ=="
  "resolved" "https://mirrors.tencent.com/npm/eslint/-/eslint-9.28.0.tgz"
  "version" "9.28.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.20.0"
    "@eslint/config-helpers" "^0.2.1"
    "@eslint/core" "^0.14.0"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.28.0"
    "@eslint/plugin-kit" "^0.3.1"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    "ajv" "^6.12.4"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.6"
    "debug" "^4.3.2"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^8.3.0"
    "eslint-visitor-keys" "^4.2.0"
    "espree" "^10.3.0"
    "esquery" "^1.5.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^8.0.0"
    "find-up" "^5.0.0"
    "glob-parent" "^6.0.2"
    "ignore" "^5.2.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.1.2"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.3"

"espree@^10.0.1", "espree@^10.3.0":
  "integrity" "sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg=="
  "resolved" "https://mirrors.tencent.com/npm/espree/-/espree-10.3.0.tgz"
  "version" "10.3.0"
  dependencies:
    "acorn" "^8.14.0"
    "acorn-jsx" "^5.3.2"
    "eslint-visitor-keys" "^4.2.0"

"esquery@^1.5.0":
  "integrity" "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg=="
  "resolved" "https://mirrors.tencent.com/npm/esquery/-/esquery-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://mirrors.tencent.com/npm/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^5.1.0", "estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://mirrors.tencent.com/npm/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://mirrors.tencent.com/npm/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://mirrors.tencent.com/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-glob@^3.3.2":
  "integrity" "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg=="
  "resolved" "https://mirrors.tencent.com/npm/fast-glob/-/fast-glob-3.3.3.tgz"
  "version" "3.3.3"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.8"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://mirrors.tencent.com/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw=="
  "resolved" "https://mirrors.tencent.com/npm/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastq@^1.6.0":
  "integrity" "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ=="
  "resolved" "https://mirrors.tencent.com/npm/fastq/-/fastq-1.19.1.tgz"
  "version" "1.19.1"
  dependencies:
    "reusify" "^1.0.4"

"fdir@^6.4.4":
  "integrity" "sha512-4BG7puHpVsIYxZUbiUE3RqGloLaSSwzYie5jvasC4LWuBWzZawynvYouhjbQKw2JuIGYdm0DzIxl8iVidKlUEw=="
  "resolved" "https://mirrors.tencent.com/npm/fdir/-/fdir-6.4.5.tgz"
  "version" "6.4.5"

"file-entry-cache@^8.0.0":
  "integrity" "sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ=="
  "resolved" "https://mirrors.tencent.com/npm/file-entry-cache/-/file-entry-cache-8.0.0.tgz"
  "version" "8.0.0"
  dependencies:
    "flat-cache" "^4.0.0"

"fill-range@^7.1.1":
  "integrity" "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg=="
  "resolved" "https://mirrors.tencent.com/npm/fill-range/-/fill-range-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"find-up@^5.0.0":
  "integrity" "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng=="
  "resolved" "https://mirrors.tencent.com/npm/find-up/-/find-up-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "locate-path" "^6.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^4.0.0":
  "integrity" "sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw=="
  "resolved" "https://mirrors.tencent.com/npm/flat-cache/-/flat-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "flatted" "^3.2.9"
    "keyv" "^4.5.4"

"flatted@^3.2.9":
  "integrity" "sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg=="
  "resolved" "https://mirrors.tencent.com/npm/flatted/-/flatted-3.3.3.tgz"
  "version" "3.3.3"

"fsevents@^2.3.2", "fsevents@~2.3.2":
  "integrity" "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA=="
  "resolved" "https://mirrors.tencent.com/npm/fsevents/-/fsevents-2.3.2.tgz"
  "version" "2.3.2"

"fsevents@~2.3.3":
  "integrity" "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw=="
  "resolved" "https://mirrors.tencent.com/npm/fsevents/-/fsevents-2.3.3.tgz"
  "version" "2.3.3"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://mirrors.tencent.com/npm/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"glob-parent@^5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://mirrors.tencent.com/npm/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.2":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://mirrors.tencent.com/npm/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://mirrors.tencent.com/npm/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^14.0.0":
  "integrity" "sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ=="
  "resolved" "https://mirrors.tencent.com/npm/globals/-/globals-14.0.0.tgz"
  "version" "14.0.0"

"globals@^16.0.0":
  "integrity" "sha512-O+7l9tPdHCU320IigZZPj5zmRCFG9xHmx9cU8FqU2Rp+JN714seHV+2S9+JslCpY4gJwU2vOGox0wzgae/MCEg=="
  "resolved" "https://mirrors.tencent.com/npm/globals/-/globals-16.2.0.tgz"
  "version" "16.2.0"

"graphemer@^1.4.0":
  "integrity" "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag=="
  "resolved" "https://mirrors.tencent.com/npm/graphemer/-/graphemer-1.4.0.tgz"
  "version" "1.4.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://mirrors.tencent.com/npm/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"ignore@^5.2.0":
  "integrity" "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g=="
  "resolved" "https://mirrors.tencent.com/npm/ignore/-/ignore-5.3.2.tgz"
  "version" "5.3.2"

"ignore@^7.0.0":
  "integrity" "sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg=="
  "resolved" "https://mirrors.tencent.com/npm/ignore/-/ignore-7.0.5.tgz"
  "version" "7.0.5"

"import-fresh@^3.2.1":
  "integrity" "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ=="
  "resolved" "https://mirrors.tencent.com/npm/import-fresh/-/import-fresh-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://mirrors.tencent.com/npm/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://mirrors.tencent.com/npm/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://mirrors.tencent.com/npm/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://mirrors.tencent.com/npm/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://mirrors.tencent.com/npm/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://mirrors.tencent.com/npm/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^4.1.0":
  "integrity" "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="
  "resolved" "https://mirrors.tencent.com/npm/js-yaml/-/js-yaml-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "argparse" "^2.0.1"

"jsesc@^3.0.2":
  "integrity" "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA=="
  "resolved" "https://mirrors.tencent.com/npm/jsesc/-/jsesc-3.1.0.tgz"
  "version" "3.1.0"

"json-buffer@3.0.1":
  "integrity" "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ=="
  "resolved" "https://mirrors.tencent.com/npm/json-buffer/-/json-buffer-3.0.1.tgz"
  "version" "3.0.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://mirrors.tencent.com/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "https://mirrors.tencent.com/npm/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json2mq@^0.2.0":
  "integrity" "sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA=="
  "resolved" "https://mirrors.tencent.com/npm/json2mq/-/json2mq-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "string-convert" "^0.2.0"

"json5@^2.2.3":
  "integrity" "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="
  "resolved" "https://mirrors.tencent.com/npm/json5/-/json5-2.2.3.tgz"
  "version" "2.2.3"

"keyv@^4.5.4":
  "integrity" "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw=="
  "resolved" "https://mirrors.tencent.com/npm/keyv/-/keyv-4.5.4.tgz"
  "version" "4.5.4"
  dependencies:
    "json-buffer" "3.0.1"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://mirrors.tencent.com/npm/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"locate-path@^6.0.0":
  "integrity" "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw=="
  "resolved" "https://mirrors.tencent.com/npm/locate-path/-/locate-path-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-locate" "^5.0.0"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://mirrors.tencent.com/npm/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://mirrors.tencent.com/npm/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"merge2@^1.3.0":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://mirrors.tencent.com/npm/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"micromatch@^4.0.8":
  "integrity" "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA=="
  "resolved" "https://mirrors.tencent.com/npm/micromatch/-/micromatch-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "braces" "^3.0.3"
    "picomatch" "^2.3.1"

"minimatch@^3.1.2":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://mirrors.tencent.com/npm/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@^9.0.4":
  "integrity" "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow=="
  "resolved" "https://mirrors.tencent.com/npm/minimatch/-/minimatch-9.0.5.tgz"
  "version" "9.0.5"
  dependencies:
    "brace-expansion" "^2.0.1"

"ms@^2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://mirrors.tencent.com/npm/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"nanoid@^3.3.11":
  "integrity" "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w=="
  "resolved" "https://mirrors.tencent.com/npm/nanoid/-/nanoid-3.3.11.tgz"
  "version" "3.3.11"

"natural-compare@^1.4.0":
  "integrity" "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "https://mirrors.tencent.com/npm/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"node-releases@^2.0.19":
  "integrity" "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw=="
  "resolved" "https://mirrors.tencent.com/npm/node-releases/-/node-releases-2.0.19.tgz"
  "version" "2.0.19"

"optionator@^0.9.3":
  "integrity" "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g=="
  "resolved" "https://mirrors.tencent.com/npm/optionator/-/optionator-0.9.4.tgz"
  "version" "0.9.4"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.5"

"p-limit@^3.0.2":
  "integrity" "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ=="
  "resolved" "https://mirrors.tencent.com/npm/p-limit/-/p-limit-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "yocto-queue" "^0.1.0"

"p-locate@^5.0.0":
  "integrity" "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw=="
  "resolved" "https://mirrors.tencent.com/npm/p-locate/-/p-locate-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-limit" "^3.0.2"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://mirrors.tencent.com/npm/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://mirrors.tencent.com/npm/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://mirrors.tencent.com/npm/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"picocolors@^1.1.1":
  "integrity" "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="
  "resolved" "https://mirrors.tencent.com/npm/picocolors/-/picocolors-1.1.1.tgz"
  "version" "1.1.1"

"picomatch@^2.3.1", "picomatch@^3 || ^4":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://mirrors.tencent.com/npm/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"picomatch@^4.0.2":
  "integrity" "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg=="
  "resolved" "https://mirrors.tencent.com/npm/picomatch/-/picomatch-4.0.2.tgz"
  "version" "4.0.2"

"postcss@^8.5.3":
  "integrity" "sha512-QSa9EBe+uwlGTFmHsPKokv3B/oEMQZxfqW0QqNCyhpa6mB1afzulwn8hihglqAb2pOw+BJgNlmXQ8la2VeHB7w=="
  "resolved" "https://mirrors.tencent.com/npm/postcss/-/postcss-8.5.4.tgz"
  "version" "8.5.4"
  dependencies:
    "nanoid" "^3.3.11"
    "picocolors" "^1.1.1"
    "source-map-js" "^1.2.1"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://mirrors.tencent.com/npm/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"punycode@^2.1.0":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://mirrors.tencent.com/npm/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://mirrors.tencent.com/npm/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"rc-cascader@~3.34.0":
  "integrity" "sha512-KpXypcvju9ptjW9FaN2NFcA2QH9E9LHKq169Y0eWtH4e/wHQ5Wh5qZakAgvb8EKZ736WZ3B0zLLOBsrsja5Dag=="
  "resolved" "https://mirrors.tencent.com/npm/rc-cascader/-/rc-cascader-3.34.0.tgz"
  "version" "3.34.0"
  dependencies:
    "@babel/runtime" "^7.25.7"
    "classnames" "^2.3.1"
    "rc-select" "~14.16.2"
    "rc-tree" "~5.13.0"
    "rc-util" "^5.43.0"

"rc-checkbox@~3.5.0":
  "integrity" "sha512-aOAQc3E98HteIIsSqm6Xk2FPKIER6+5vyEFMZfo73TqM+VVAIqOkHoPjgKLqSNtVLWScoaM7vY2ZrGEheI79yg=="
  "resolved" "https://mirrors.tencent.com/npm/rc-checkbox/-/rc-checkbox-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.3.2"
    "rc-util" "^5.25.2"

"rc-collapse@~3.9.0":
  "integrity" "sha512-swDdz4QZ4dFTo4RAUMLL50qP0EY62N2kvmk2We5xYdRwcRn8WcYtuetCJpwpaCbUfUt5+huLpVxhvmnK+PHrkA=="
  "resolved" "https://mirrors.tencent.com/npm/rc-collapse/-/rc-collapse-3.9.0.tgz"
  "version" "3.9.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.3.4"
    "rc-util" "^5.27.0"

"rc-dialog@~9.6.0":
  "integrity" "sha512-ApoVi9Z8PaCQg6FsUzS8yvBEQy0ZL2PkuvAgrmohPkN3okps5WZ5WQWPc1RNuiOKaAYv8B97ACdsFU5LizzCqg=="
  "resolved" "https://mirrors.tencent.com/npm/rc-dialog/-/rc-dialog-9.6.0.tgz"
  "version" "9.6.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/portal" "^1.0.0-8"
    "classnames" "^2.2.6"
    "rc-motion" "^2.3.0"
    "rc-util" "^5.21.0"

"rc-drawer@~7.3.0":
  "integrity" "sha512-DX6CIgiBWNpJIMGFO8BAISFkxiuKitoizooj4BDyee8/SnBn0zwO2FHrNDpqqepj0E/TFTDpmEBCyFuTgC7MOg=="
  "resolved" "https://mirrors.tencent.com/npm/rc-drawer/-/rc-drawer-7.3.0.tgz"
  "version" "7.3.0"
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@rc-component/portal" "^1.1.1"
    "classnames" "^2.2.6"
    "rc-motion" "^2.6.1"
    "rc-util" "^5.38.1"

"rc-dropdown@~4.2.0", "rc-dropdown@~4.2.1":
  "integrity" "sha512-YDAlXsPv3I1n42dv1JpdM7wJ+gSUBfeyPK59ZpBD9jQhK9jVuxpjj3NmWQHOBceA1zEPVX84T2wbdb2SD0UjmA=="
  "resolved" "https://mirrors.tencent.com/npm/rc-dropdown/-/rc-dropdown-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.2.6"
    "rc-util" "^5.44.1"

"rc-field-form@~2.7.0":
  "integrity" "sha512-hgKsCay2taxzVnBPZl+1n4ZondsV78G++XVsMIJCAoioMjlMQR9YwAp7JZDIECzIu2Z66R+f4SFIRrO2DjDNAA=="
  "resolved" "https://mirrors.tencent.com/npm/rc-field-form/-/rc-field-form-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/async-validator" "^5.0.3"
    "rc-util" "^5.32.2"

"rc-image@~7.12.0":
  "integrity" "sha512-cZ3HTyyckPnNnUb9/DRqduqzLfrQRyi+CdHjdqgsyDpI3Ln5UX1kXnAhPBSJj9pVRzwRFgqkN7p9b6HBDjmu/Q=="
  "resolved" "https://mirrors.tencent.com/npm/rc-image/-/rc-image-7.12.0.tgz"
  "version" "7.12.0"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/portal" "^1.0.2"
    "classnames" "^2.2.6"
    "rc-dialog" "~9.6.0"
    "rc-motion" "^2.6.2"
    "rc-util" "^5.34.1"

"rc-input-number@~9.5.0":
  "integrity" "sha512-bKaEvB5tHebUURAEXw35LDcnRZLq3x1k7GxfAqBMzmpHkDGzjAtnUL8y4y5N15rIFIg5IJgwr211jInl3cipag=="
  "resolved" "https://mirrors.tencent.com/npm/rc-input-number/-/rc-input-number-9.5.0.tgz"
  "version" "9.5.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/mini-decimal" "^1.0.1"
    "classnames" "^2.2.5"
    "rc-input" "~1.8.0"
    "rc-util" "^5.40.1"

"rc-input@~1.8.0":
  "integrity" "sha512-KXvaTbX+7ha8a/k+eg6SYRVERK0NddX8QX7a7AnRvUa/rEH0CNMlpcBzBkhI0wp2C8C4HlMoYl8TImSN+fuHKA=="
  "resolved" "https://mirrors.tencent.com/npm/rc-input/-/rc-input-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.18.1"

"rc-mentions@~2.20.0":
  "integrity" "sha512-w8HCMZEh3f0nR8ZEd466ATqmXFCMGMN5UFCzEUL0bM/nGw/wOS2GgRzKBcm19K++jDyuWCOJOdgcKGXU3fXfbQ=="
  "resolved" "https://mirrors.tencent.com/npm/rc-mentions/-/rc-mentions-2.20.0.tgz"
  "version" "2.20.0"
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.2.6"
    "rc-input" "~1.8.0"
    "rc-menu" "~9.16.0"
    "rc-textarea" "~1.10.0"
    "rc-util" "^5.34.1"

"rc-menu@~9.16.0", "rc-menu@~9.16.1":
  "integrity" "sha512-ghHx6/6Dvp+fw8CJhDUHFHDJ84hJE3BXNCzSgLdmNiFErWSOaZNsihDAsKq9ByTALo/xkNIwtDFGIl6r+RPXBg=="
  "resolved" "https://mirrors.tencent.com/npm/rc-menu/-/rc-menu-9.16.1.tgz"
  "version" "9.16.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "2.x"
    "rc-motion" "^2.4.3"
    "rc-overflow" "^1.3.1"
    "rc-util" "^5.27.0"

"rc-motion@^2.0.0", "rc-motion@^2.0.1", "rc-motion@^2.3.0", "rc-motion@^2.3.4", "rc-motion@^2.4.3", "rc-motion@^2.4.4", "rc-motion@^2.6.1", "rc-motion@^2.6.2", "rc-motion@^2.9.0", "rc-motion@^2.9.5":
  "integrity" "sha512-w+XTUrfh7ArbYEd2582uDrEhmBHwK1ZENJiSJVb7uRxdE7qJSYjbO2eksRXmndqyKqKoYPc9ClpPh5242mV1vA=="
  "resolved" "https://mirrors.tencent.com/npm/rc-motion/-/rc-motion-2.9.5.tgz"
  "version" "2.9.5"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.44.0"

"rc-notification@~5.6.4":
  "integrity" "sha512-KcS4O6B4qzM3KH7lkwOB7ooLPZ4b6J+VMmQgT51VZCeEcmghdeR4IrMcFq0LG+RPdnbe/ArT086tGM8Snimgiw=="
  "resolved" "https://mirrors.tencent.com/npm/rc-notification/-/rc-notification-5.6.4.tgz"
  "version" "5.6.4"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.9.0"
    "rc-util" "^5.20.1"

"rc-overflow@^1.3.1", "rc-overflow@^1.3.2":
  "integrity" "sha512-3MoPQQPV1uKyOMVNd6SZfONi+f3st0r8PksexIdBTeIYbMX0Jr+k7pHEDvsXtR4BpCv90/Pv2MovVNhktKrwvw=="
  "resolved" "https://mirrors.tencent.com/npm/rc-overflow/-/rc-overflow-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.37.0"

"rc-pagination@~5.1.0":
  "integrity" "sha512-8416Yip/+eclTFdHXLKTxZvn70duYVGTvUUWbckCCZoIl3jagqke3GLsFrMs0bsQBikiYpZLD9206Ej4SOdOXQ=="
  "resolved" "https://mirrors.tencent.com/npm/rc-pagination/-/rc-pagination-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.3.2"
    "rc-util" "^5.38.0"

"rc-picker@~4.11.3":
  "integrity" "sha512-MJ5teb7FlNE0NFHTncxXQ62Y5lytq6sh5nUw0iH8OkHL/TjARSEvSHpr940pWgjGANpjCwyMdvsEV55l5tYNSg=="
  "resolved" "https://mirrors.tencent.com/npm/rc-picker/-/rc-picker-4.11.3.tgz"
  "version" "4.11.3"
  dependencies:
    "@babel/runtime" "^7.24.7"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.2.1"
    "rc-overflow" "^1.3.2"
    "rc-resize-observer" "^1.4.0"
    "rc-util" "^5.43.0"

"rc-progress@~4.0.0":
  "integrity" "sha512-oofVMMafOCokIUIBnZLNcOZFsABaUw8PPrf1/y0ZBvKZNpOiu5h4AO9vv11Sw0p4Hb3D0yGWuEattcQGtNJ/aw=="
  "resolved" "https://mirrors.tencent.com/npm/rc-progress/-/rc-progress-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.6"
    "rc-util" "^5.16.1"

"rc-rate@~2.13.1":
  "integrity" "sha512-QUhQ9ivQ8Gy7mtMZPAjLbxBt5y9GRp65VcUyGUMF3N3fhiftivPHdpuDIaWIMOTEprAjZPC08bls1dQB+I1F2Q=="
  "resolved" "https://mirrors.tencent.com/npm/rc-rate/-/rc-rate-2.13.1.tgz"
  "version" "2.13.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.0.1"

"rc-resize-observer@^1.0.0", "rc-resize-observer@^1.1.0", "rc-resize-observer@^1.3.1", "rc-resize-observer@^1.4.0", "rc-resize-observer@^1.4.3":
  "integrity" "sha512-YZLjUbyIWox8E9i9C3Tm7ia+W7euPItNWSPX5sCcQTYbnwDb5uNpnLHQCG1f22oZWUhLw4Mv2tFmeWe68CDQRQ=="
  "resolved" "https://mirrors.tencent.com/npm/rc-resize-observer/-/rc-resize-observer-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "@babel/runtime" "^7.20.7"
    "classnames" "^2.2.1"
    "rc-util" "^5.44.1"
    "resize-observer-polyfill" "^1.5.1"

"rc-segmented@~2.7.0":
  "integrity" "sha512-liijAjXz+KnTRVnxxXG2sYDGd6iLL7VpGGdR8gwoxAXy2KglviKCxLWZdjKYJzYzGSUwKDSTdYk8brj54Bn5BA=="
  "resolved" "https://mirrors.tencent.com/npm/rc-segmented/-/rc-segmented-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-motion" "^2.4.4"
    "rc-util" "^5.17.0"

"rc-select@~14.16.2", "rc-select@~14.16.8":
  "integrity" "sha512-NOV5BZa1wZrsdkKaiK7LHRuo5ZjZYMDxPP6/1+09+FB4KoNi8jcG1ZqLE3AVCxEsYMBe65OBx71wFoHRTP3LRg=="
  "resolved" "https://mirrors.tencent.com/npm/rc-select/-/rc-select-14.16.8.tgz"
  "version" "14.16.8"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.1.1"
    "classnames" "2.x"
    "rc-motion" "^2.0.1"
    "rc-overflow" "^1.3.1"
    "rc-util" "^5.16.1"
    "rc-virtual-list" "^3.5.2"

"rc-slider@~11.1.8":
  "integrity" "sha512-2gg/72YFSpKP+Ja5AjC5DPL1YnV8DEITDQrcc1eASrUYjl0esptaBVJBh5nLTXCCp15eD8EuGjwezVGSHhs9tQ=="
  "resolved" "https://mirrors.tencent.com/npm/rc-slider/-/rc-slider-11.1.8.tgz"
  "version" "11.1.8"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.36.0"

"rc-steps@~6.0.1":
  "integrity" "sha512-lKHL+Sny0SeHkQKKDJlAjV5oZ8DwCdS2hFhAkIjuQt1/pB81M0cA0ErVFdHq9+jmPmFw1vJB2F5NBzFXLJxV+g=="
  "resolved" "https://mirrors.tencent.com/npm/rc-steps/-/rc-steps-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "@babel/runtime" "^7.16.7"
    "classnames" "^2.2.3"
    "rc-util" "^5.16.1"

"rc-switch@~4.1.0":
  "integrity" "sha512-TI8ufP2Az9oEbvyCeVE4+90PDSljGyuwix3fV58p7HV2o4wBnVToEyomJRVyTaZeqNPAp+vqeo4Wnj5u0ZZQBg=="
  "resolved" "https://mirrors.tencent.com/npm/rc-switch/-/rc-switch-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@babel/runtime" "^7.21.0"
    "classnames" "^2.2.1"
    "rc-util" "^5.30.0"

"rc-table@~7.50.5":
  "integrity" "sha512-FDZu8aolhSYd3v9KOc3lZOVAU77wmRRu44R0Wfb8Oj1dXRUsloFaXMSl6f7yuWZUxArJTli7k8TEOX2mvhDl4A=="
  "resolved" "https://mirrors.tencent.com/npm/rc-table/-/rc-table-7.50.5.tgz"
  "version" "7.50.5"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/context" "^1.4.0"
    "classnames" "^2.2.5"
    "rc-resize-observer" "^1.1.0"
    "rc-util" "^5.44.3"
    "rc-virtual-list" "^3.14.2"

"rc-tabs@~15.6.1":
  "integrity" "sha512-/HzDV1VqOsUWyuC0c6AkxVYFjvx9+rFPKZ32ejxX0Uc7QCzcEjTA9/xMgv4HemPKwzBNX8KhGVbbumDjnj92aA=="
  "resolved" "https://mirrors.tencent.com/npm/rc-tabs/-/rc-tabs-15.6.1.tgz"
  "version" "15.6.1"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "classnames" "2.x"
    "rc-dropdown" "~4.2.0"
    "rc-menu" "~9.16.0"
    "rc-motion" "^2.6.2"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.34.1"

"rc-textarea@~1.10.0":
  "integrity" "sha512-ai9IkanNuyBS4x6sOL8qu/Ld40e6cEs6pgk93R+XLYg0mDSjNBGey6/ZpDs5+gNLD7urQ14po3V6Ck2dJLt9SA=="
  "resolved" "https://mirrors.tencent.com/npm/rc-textarea/-/rc-textarea-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "rc-input" "~1.8.0"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.27.0"

"rc-tooltip@~6.4.0":
  "integrity" "sha512-kqyivim5cp8I5RkHmpsp1Nn/Wk+1oeloMv9c7LXNgDxUpGm+RbXJGL+OPvDlcRnx9DBeOe4wyOIl4OKUERyH1g=="
  "resolved" "https://mirrors.tencent.com/npm/rc-tooltip/-/rc-tooltip-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.3.1"
    "rc-util" "^5.44.3"

"rc-tree-select@~5.27.0":
  "integrity" "sha512-2qTBTzwIT7LRI1o7zLyrCzmo5tQanmyGbSaGTIf7sYimCklAToVVfpMC6OAldSKolcnjorBYPNSKQqJmN3TCww=="
  "resolved" "https://mirrors.tencent.com/npm/rc-tree-select/-/rc-tree-select-5.27.0.tgz"
  "version" "5.27.0"
  dependencies:
    "@babel/runtime" "^7.25.7"
    "classnames" "2.x"
    "rc-select" "~14.16.2"
    "rc-tree" "~5.13.0"
    "rc-util" "^5.43.0"

"rc-tree@~5.13.0", "rc-tree@~5.13.1":
  "integrity" "sha512-FNhIefhftobCdUJshO7M8uZTA9F4OPGVXqGfZkkD/5soDeOhwO06T/aKTrg0WD8gRg/pyfq+ql3aMymLHCTC4A=="
  "resolved" "https://mirrors.tencent.com/npm/rc-tree/-/rc-tree-5.13.1.tgz"
  "version" "5.13.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.0.1"
    "rc-util" "^5.16.1"
    "rc-virtual-list" "^3.5.1"

"rc-upload@~4.9.2":
  "integrity" "sha512-nHx+9rbd1FKMiMRYsqQ3NkXUv7COHPBo3X1Obwq9SWS6/diF/A0aJ5OHubvwUAIDs+4RMleljV0pcrNUc823GQ=="
  "resolved" "https://mirrors.tencent.com/npm/rc-upload/-/rc-upload-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "classnames" "^2.2.5"
    "rc-util" "^5.2.0"

"rc-util@^5.0.1", "rc-util@^5.16.1", "rc-util@^5.17.0", "rc-util@^5.18.1", "rc-util@^5.2.0", "rc-util@^5.20.1", "rc-util@^5.21.0", "rc-util@^5.24.4", "rc-util@^5.25.2", "rc-util@^5.27.0", "rc-util@^5.30.0", "rc-util@^5.31.1", "rc-util@^5.32.2", "rc-util@^5.34.1", "rc-util@^5.35.0", "rc-util@^5.36.0", "rc-util@^5.37.0", "rc-util@^5.38.0", "rc-util@^5.38.1", "rc-util@^5.40.1", "rc-util@^5.43.0", "rc-util@^5.44.0", "rc-util@^5.44.1", "rc-util@^5.44.3", "rc-util@^5.44.4":
  "integrity" "sha512-resueRJzmHG9Q6rI/DfK6Kdv9/Lfls05vzMs1Sk3M2P+3cJa+MakaZyWY8IPfehVuhPJFKrIY1IK4GqbiaiY5w=="
  "resolved" "https://mirrors.tencent.com/npm/rc-util/-/rc-util-5.44.4.tgz"
  "version" "5.44.4"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "react-is" "^18.2.0"

"rc-virtual-list@^3.14.2", "rc-virtual-list@^3.5.1", "rc-virtual-list@^3.5.2":
  "integrity" "sha512-TQ5SsutL3McvWmmxqQtMIbfeoE3dGjJrRSfKekgby7WQMpPIFvv4ghytp5Z0s3D8Nik9i9YNOCqHBfk86AwgAA=="
  "resolved" "https://mirrors.tencent.com/npm/rc-virtual-list/-/rc-virtual-list-3.18.6.tgz"
  "version" "3.18.6"
  dependencies:
    "@babel/runtime" "^7.20.0"
    "classnames" "^2.2.6"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.36.0"

"react-dom@*", "react-dom@^19.1.0", "react-dom@>=16.0.0", "react-dom@>=16.11.0", "react-dom@>=16.9.0":
  "integrity" "sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g=="
  "resolved" "https://mirrors.tencent.com/npm/react-dom/-/react-dom-19.1.0.tgz"
  "version" "19.1.0"
  dependencies:
    "scheduler" "^0.26.0"

"react-is@^18.2.0":
  "integrity" "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="
  "resolved" "https://mirrors.tencent.com/npm/react-is/-/react-is-18.3.1.tgz"
  "version" "18.3.1"

"react-refresh@^0.17.0":
  "integrity" "sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ=="
  "resolved" "https://mirrors.tencent.com/npm/react-refresh/-/react-refresh-0.17.0.tgz"
  "version" "0.17.0"

"react@*", "react@^19.1.0", "react@>=16.0.0", "react@>=16.11.0", "react@>=16.9.0":
  "integrity" "sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg=="
  "resolved" "https://mirrors.tencent.com/npm/react/-/react-19.1.0.tgz"
  "version" "19.1.0"

"resize-observer-polyfill@^1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://mirrors.tencent.com/npm/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://mirrors.tencent.com/npm/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"reusify@^1.0.4":
  "integrity" "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw=="
  "resolved" "https://mirrors.tencent.com/npm/reusify/-/reusify-1.1.0.tgz"
  "version" "1.1.0"

"rollup@^4.34.9":
  "integrity" "sha512-cPmwD3FnFv8rKMBc1MxWCwVQFxwf1JEmSX3iQXrRVVG15zerAIXRjMFVWnd5Q5QvgKF7Aj+5ykXFhUl+QGnyOw=="
  "resolved" "https://mirrors.tencent.com/npm/rollup/-/rollup-4.41.1.tgz"
  "version" "4.41.1"
  dependencies:
    "@rollup/rollup-android-arm-eabi" "4.41.1"
    "@rollup/rollup-android-arm64" "4.41.1"
    "@rollup/rollup-darwin-arm64" "4.41.1"
    "@rollup/rollup-darwin-x64" "4.41.1"
    "@rollup/rollup-freebsd-arm64" "4.41.1"
    "@rollup/rollup-freebsd-x64" "4.41.1"
    "@rollup/rollup-linux-arm-gnueabihf" "4.41.1"
    "@rollup/rollup-linux-arm-musleabihf" "4.41.1"
    "@rollup/rollup-linux-arm64-gnu" "4.41.1"
    "@rollup/rollup-linux-arm64-musl" "4.41.1"
    "@rollup/rollup-linux-loongarch64-gnu" "4.41.1"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.41.1"
    "@rollup/rollup-linux-riscv64-gnu" "4.41.1"
    "@rollup/rollup-linux-riscv64-musl" "4.41.1"
    "@rollup/rollup-linux-s390x-gnu" "4.41.1"
    "@rollup/rollup-linux-x64-gnu" "4.41.1"
    "@rollup/rollup-linux-x64-musl" "4.41.1"
    "@rollup/rollup-win32-arm64-msvc" "4.41.1"
    "@rollup/rollup-win32-ia32-msvc" "4.41.1"
    "@rollup/rollup-win32-x64-msvc" "4.41.1"
    "@types/estree" "1.0.7"
    "fsevents" "~2.3.2"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.41.1"
    "@rollup/rollup-android-arm64" "4.41.1"
    "@rollup/rollup-darwin-arm64" "4.41.1"
    "@rollup/rollup-darwin-x64" "4.41.1"
    "@rollup/rollup-freebsd-arm64" "4.41.1"
    "@rollup/rollup-freebsd-x64" "4.41.1"
    "@rollup/rollup-linux-arm-gnueabihf" "4.41.1"
    "@rollup/rollup-linux-arm-musleabihf" "4.41.1"
    "@rollup/rollup-linux-arm64-gnu" "4.41.1"
    "@rollup/rollup-linux-arm64-musl" "4.41.1"
    "@rollup/rollup-linux-loongarch64-gnu" "4.41.1"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.41.1"
    "@rollup/rollup-linux-riscv64-gnu" "4.41.1"
    "@rollup/rollup-linux-riscv64-musl" "4.41.1"
    "@rollup/rollup-linux-s390x-gnu" "4.41.1"
    "@rollup/rollup-linux-x64-gnu" "4.41.1"
    "@rollup/rollup-linux-x64-musl" "4.41.1"
    "@rollup/rollup-win32-arm64-msvc" "4.41.1"
    "@rollup/rollup-win32-ia32-msvc" "4.41.1"
    "@rollup/rollup-win32-x64-msvc" "4.41.1"
    "fsevents" "~2.3.2"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://mirrors.tencent.com/npm/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"scheduler@^0.26.0":
  "integrity" "sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA=="
  "resolved" "https://mirrors.tencent.com/npm/scheduler/-/scheduler-0.26.0.tgz"
  "version" "0.26.0"

"scroll-into-view-if-needed@^3.1.0":
  "integrity" "sha512-49oNpRjWRvnU8NyGVmUaYG4jtTkNonFZI86MmGRDqBphEK2EXT9gdEUoQPZhuBM8yWHxCWbobltqYO5M4XrUvQ=="
  "resolved" "https://mirrors.tencent.com/npm/scroll-into-view-if-needed/-/scroll-into-view-if-needed-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "compute-scroll-into-view" "^3.0.2"

"semver@^6.3.1":
  "integrity" "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="
  "resolved" "https://mirrors.tencent.com/npm/semver/-/semver-6.3.1.tgz"
  "version" "6.3.1"

"semver@^7.6.0":
  "integrity" "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA=="
  "resolved" "https://mirrors.tencent.com/npm/semver/-/semver-7.7.2.tgz"
  "version" "7.7.2"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://mirrors.tencent.com/npm/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://mirrors.tencent.com/npm/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"source-map-js@^1.2.1":
  "integrity" "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="
  "resolved" "https://mirrors.tencent.com/npm/source-map-js/-/source-map-js-1.2.1.tgz"
  "version" "1.2.1"

"string-convert@^0.2.0":
  "integrity" "sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A=="
  "resolved" "https://mirrors.tencent.com/npm/string-convert/-/string-convert-0.2.1.tgz"
  "version" "0.2.1"

"strip-json-comments@^3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://mirrors.tencent.com/npm/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"stylis@^4.3.4":
  "integrity" "sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ=="
  "resolved" "https://mirrors.tencent.com/npm/stylis/-/stylis-4.3.6.tgz"
  "version" "4.3.6"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://mirrors.tencent.com/npm/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"throttle-debounce@^5.0.0", "throttle-debounce@^5.0.2":
  "integrity" "sha512-B71/4oyj61iNH0KeCamLuE2rmKuTO5byTOSVwECM5FA7TiAiAW+UqTKZ9ERueC4qvgSttUhdmq1mXC3kJqGX7A=="
  "resolved" "https://mirrors.tencent.com/npm/throttle-debounce/-/throttle-debounce-5.0.2.tgz"
  "version" "5.0.2"

"tinyglobby@^0.2.13":
  "integrity" "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ=="
  "resolved" "https://mirrors.tencent.com/npm/tinyglobby/-/tinyglobby-0.2.14.tgz"
  "version" "0.2.14"
  dependencies:
    "fdir" "^6.4.4"
    "picomatch" "^4.0.2"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://mirrors.tencent.com/npm/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"toggle-selection@^1.0.6":
  "integrity" "sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ=="
  "resolved" "https://mirrors.tencent.com/npm/toggle-selection/-/toggle-selection-1.0.6.tgz"
  "version" "1.0.6"

"ts-api-utils@^2.1.0":
  "integrity" "sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ=="
  "resolved" "https://mirrors.tencent.com/npm/ts-api-utils/-/ts-api-utils-2.1.0.tgz"
  "version" "2.1.0"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://mirrors.tencent.com/npm/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"typescript-eslint@^8.30.1":
  "integrity" "sha512-AgRnV4sKkWOiZ0Kjbnf5ytTJXMUZQ0qhSVdQtDNYLPLnjsATEYhaO94GlRQwi4t4gO8FfjM6NnikHeKjUm8D7A=="
  "resolved" "https://mirrors.tencent.com/npm/typescript-eslint/-/typescript-eslint-8.33.1.tgz"
  "version" "8.33.1"
  dependencies:
    "@typescript-eslint/eslint-plugin" "8.33.1"
    "@typescript-eslint/parser" "8.33.1"
    "@typescript-eslint/utils" "8.33.1"

"typescript@>=4.8.4", "typescript@>=4.8.4 <5.9.0", "typescript@~5.8.3":
  "integrity" "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ=="
  "resolved" "https://mirrors.tencent.com/npm/typescript/-/typescript-5.8.3.tgz"
  "version" "5.8.3"

"update-browserslist-db@^1.1.3":
  "integrity" "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw=="
  "resolved" "https://mirrors.tencent.com/npm/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "escalade" "^3.2.0"
    "picocolors" "^1.1.1"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://mirrors.tencent.com/npm/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"vite@^4.2.0 || ^5.0.0 || ^6.0.0", "vite@^6.3.5":
  "integrity" "sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ=="
  "resolved" "https://mirrors.tencent.com/npm/vite/-/vite-6.3.5.tgz"
  "version" "6.3.5"
  dependencies:
    "esbuild" "^0.25.0"
    "fdir" "^6.4.4"
    "fsevents" "~2.3.3"
    "picomatch" "^4.0.2"
    "postcss" "^8.5.3"
    "rollup" "^4.34.9"
    "tinyglobby" "^0.2.13"
  optionalDependencies:
    "fsevents" "~2.3.3"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://mirrors.tencent.com/npm/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"word-wrap@^1.2.5":
  "integrity" "sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA=="
  "resolved" "https://mirrors.tencent.com/npm/word-wrap/-/word-wrap-1.2.5.tgz"
  "version" "1.2.5"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://mirrors.tencent.com/npm/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yocto-queue@^0.1.0":
  "integrity" "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q=="
  "resolved" "https://mirrors.tencent.com/npm/yocto-queue/-/yocto-queue-0.1.0.tgz"
  "version" "0.1.0"
