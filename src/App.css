#root {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: left;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 表格行样式 */
.table-row-light {
  background-color: #fafafa;
}

.table-row-dark {
  background-color: #ffffff;
}

/* 表格样式优化 */
.ant-table {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ant-table-thead > tr > th {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
  color: #262626;
  padding: 12px 16px;
  font-size: 13px;
}

.ant-table-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
  vertical-align: middle;
}

.ant-table-tbody > tr:hover > td {
  background-color: #e6f7ff;
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.ant-card-body {
  padding: 16px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.ant-btn-link {
  padding: 0;
  height: auto;
  line-height: 1.5;
}

/* 开关样式 */
.ant-switch {
  background-color: #bfbfbf;
}

.ant-switch-checked {
  background-color: #52c41a;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  border: none;
}

/* 输入框样式 */
.ant-input {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 选择器样式 */
.ant-select {
  border-radius: 6px;
}

.ant-select-selector {
  border-radius: 6px !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-select-focused .ant-select-selector {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 分页样式 */
.ant-pagination {
  text-align: center;
  margin-top: 16px;
}

.ant-pagination-item {
  border-radius: 4px;
}

.ant-pagination-item-active {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* 文字样式 */
.ant-typography {
  color: #262626;
}

.ant-typography-caption {
  color: #8c8c8c;
  font-size: 12px;
}

/* 图标样式 */
.anticon {
  font-size: 14px;
}

/* 空间组件样式 */
.ant-space {
  display: flex;
  align-items: center;
}

/* 气泡确认框样式 */
.ant-popover {
  border-radius: 6px;
}

/* 消息提示样式 */
.ant-message {
  top: 24px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .ant-table {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .ant-table {
    font-size: 11px;
  }
  
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
  
  .ant-card-body {
    padding: 12px;
  }
}

/* 自定义滚动条 */
.ant-table-body::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background-color: #bfbfbf;
}

.ant-table-body::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}
